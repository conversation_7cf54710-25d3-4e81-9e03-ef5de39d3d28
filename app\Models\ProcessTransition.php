<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class ProcessTransition extends Model
{
    use HasUuids;
    
    protected $table = 'process_transitions';
    protected $primaryKey = 'id';

    protected $fillable = [
        'name',
        'slug',
        'from_stage_id',
        'to_process_id',
        'type_create',
        'action_id',
        'process_version_id',
    ];
    
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    
    /**
     * <PERSON><PERSON><PERSON> quan hệ với bảng process_transitions_conditions
     */
    public function processTransitionsConditions()
    {
        return $this->hasMany('App\Models\ProcessTransitionsCondition', 'process_transition_id', 'id');
    }

    public function action()
    {
        return $this->belongsTo('App\Models\Action', 'action_id', 'id');
    }   
}
